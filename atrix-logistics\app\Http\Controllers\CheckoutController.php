<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Order;
use App\Models\UserAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class CheckoutController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show checkout page
     */
    public function index(): View
    {
        $cart = Cart::getCurrent();
        
        if ($cart->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty.');
        }

        $cart->load(['items.product']);
        $user = Auth::user();
        $addresses = $user->addresses()->get();
        $defaultShipping = $addresses->where('type', 'shipping')->where('is_default', true)->first();
        $defaultBilling = $addresses->where('type', 'billing')->where('is_default', true)->first();

        return view('frontend.checkout.index', compact('cart', 'addresses', 'defaultShipping', 'defaultBilling'));
    }

    /**
     * Process checkout
     */
    public function process(Request $request): JsonResponse
    {
        $request->validate([
            'shipping_address_id' => 'required|exists:user_addresses,id',
            'billing_address_id' => 'required|exists:user_addresses,id',
            'payment_method' => 'required|in:manual,paypal,stripe',
            'notes' => 'nullable|string|max:1000',
        ]);

        $cart = Cart::getCurrent();
        
        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.',
            ], 400);
        }

        $user = Auth::user();
        
        // Verify addresses belong to user
        $shippingAddress = $user->addresses()->findOrFail($request->shipping_address_id);
        $billingAddress = $user->addresses()->findOrFail($request->billing_address_id);

        try {
            DB::beginTransaction();

            // Create order
            $order = Order::create([
                'user_id' => $user->id,
                'order_number' => $this->generateOrderNumber(),
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'shipping_amount' => $cart->shipping_amount,
                'discount_amount' => $cart->discount_amount,
                'total_amount' => $cart->total_amount,
                'currency' => $cart->currency,
                'notes' => $request->notes,
                'shipping_address' => $shippingAddress->toArray(),
                'billing_address' => $billingAddress->toArray(),
            ]);

            // Create order items
            foreach ($cart->items as $cartItem) {
                $order->items()->create([
                    'product_id' => $cartItem->product_id,
                    'quantity' => $cartItem->quantity,
                    'unit_price' => $cartItem->unit_price,
                    'total_price' => $cartItem->total_price,
                    'product_name' => $cartItem->product->name,
                    'product_sku' => $cartItem->product->sku,
                    'product_options' => $cartItem->product_options,
                ]);

                // Update stock if managed
                if ($cartItem->product->manage_stock) {
                    $cartItem->product->decrement('stock_quantity', $cartItem->quantity);
                }
            }

            // Clear cart
            $cart->clear();

            DB::commit();

            // Handle payment based on method
            $paymentResponse = $this->handlePayment($order, $request->payment_method);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully.',
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_response' => $paymentResponse,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to process order. Please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle payment processing
     */
    private function handlePayment(Order $order, string $paymentMethod): array
    {
        switch ($paymentMethod) {
            case 'manual':
                return [
                    'type' => 'manual',
                    'message' => 'Order placed successfully. Payment instructions will be sent via email.',
                    'redirect' => route('orders.show', $order),
                ];

            case 'paypal':
                // Integrate with PayPal (similar to existing implementation)
                return [
                    'type' => 'paypal',
                    'message' => 'Redirecting to PayPal...',
                    'redirect' => route('payment.paypal', $order),
                ];

            case 'stripe':
                // Integrate with Stripe (similar to existing implementation)
                return [
                    'type' => 'stripe',
                    'message' => 'Processing payment...',
                    'redirect' => route('payment.stripe', $order),
                ];

            default:
                throw new \Exception('Invalid payment method');
        }
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = now()->format('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Show order confirmation
     */
    public function confirmation(Order $order): View
    {
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }

        $order->load(['items.product']);

        return view('frontend.checkout.confirmation', compact('order'));
    }
}
