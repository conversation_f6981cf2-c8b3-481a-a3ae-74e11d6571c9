@extends('layouts.admin')

@section('title', '<PERSON> Pa<PERSON>el - ' . $parcel->tracking_number)
@section('page-title', 'Edit Parcel')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.parcels.show', $parcel) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Details
        </a>
        <a href="{{ route('admin.parcels.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to List
        </a>
    </div>
@endsection

@section('content')
    <form method="POST" action="{{ route('admin.parcels.update', $parcel) }}" id="parcelForm">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <!-- Current Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>Current Status
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Tracking Number</label>
                                <div class="form-control bg-light">{{ $parcel->tracking_number }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="pending" {{ old('status', $parcel->status) === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="picked_up" {{ old('status', $parcel->status) === 'picked_up' ? 'selected' : '' }}>Picked Up</option>
                                    <option value="in_transit" {{ old('status', $parcel->status) === 'in_transit' ? 'selected' : '' }}>In Transit</option>
                                    <option value="out_for_delivery" {{ old('status', $parcel->status) === 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                                    <option value="delivered" {{ old('status', $parcel->status) === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                    <option value="exception" {{ old('status', $parcel->status) === 'exception' ? 'selected' : '' }}>Exception</option>
                                    <option value="returned" {{ old('status', $parcel->status) === 'returned' ? 'selected' : '' }}>Returned</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Created</label>
                                <div class="form-control bg-light">{{ $parcel->created_at->format('M d, Y h:i A') }}</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_paid" name="is_paid" value="1" 
                                           {{ old('is_paid', $parcel->is_paid) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_paid">
                                        Payment Received
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sender Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Sender Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sender_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_name') is-invalid @enderror" 
                                       id="sender_name" name="sender_name" value="{{ old('sender_name', $parcel->sender_name) }}" required>
                                @error('sender_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sender_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('sender_email') is-invalid @enderror" 
                                       id="sender_email" name="sender_email" value="{{ old('sender_email', $parcel->sender_email) }}" required>
                                @error('sender_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sender_phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('sender_phone') is-invalid @enderror" 
                                       id="sender_phone" name="sender_phone" value="{{ old('sender_phone', $parcel->sender_phone) }}" required>
                                @error('sender_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sender_country" class="form-label">Country <span class="text-danger">*</span></label>
                                <select class="form-select @error('sender_country') is-invalid @enderror" 
                                        id="sender_country" name="sender_country" required>
                                    <option value="">Select Country</option>
                                    <option value="USA" {{ old('sender_country', $parcel->sender_country) === 'USA' ? 'selected' : '' }}>United States</option>
                                    <option value="Canada" {{ old('sender_country', $parcel->sender_country) === 'Canada' ? 'selected' : '' }}>Canada</option>
                                    <option value="Mexico" {{ old('sender_country', $parcel->sender_country) === 'Mexico' ? 'selected' : '' }}>Mexico</option>
                                    <option value="UK" {{ old('sender_country', $parcel->sender_country) === 'UK' ? 'selected' : '' }}>United Kingdom</option>
                                </select>
                                @error('sender_country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="sender_address" class="form-label">Street Address <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('sender_address') is-invalid @enderror" 
                                      id="sender_address" name="sender_address" rows="2" required>{{ old('sender_address', $parcel->sender_address) }}</textarea>
                            @error('sender_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="sender_city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_city') is-invalid @enderror" 
                                       id="sender_city" name="sender_city" value="{{ old('sender_city', $parcel->sender_city) }}" required>
                                @error('sender_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sender_state" class="form-label">State/Province <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_state') is-invalid @enderror" 
                                       id="sender_state" name="sender_state" value="{{ old('sender_state', $parcel->sender_state) }}" required>
                                @error('sender_state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sender_postal_code" class="form-label">Postal Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_postal_code') is-invalid @enderror" 
                                       id="sender_postal_code" name="sender_postal_code" value="{{ old('sender_postal_code', $parcel->sender_postal_code) }}" required>
                                @error('sender_postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recipient Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>Recipient Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="recipient_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_name') is-invalid @enderror" 
                                       id="recipient_name" name="recipient_name" value="{{ old('recipient_name', $parcel->recipient_name) }}" required>
                                @error('recipient_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="recipient_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('recipient_email') is-invalid @enderror" 
                                       id="recipient_email" name="recipient_email" value="{{ old('recipient_email', $parcel->recipient_email) }}" required>
                                @error('recipient_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="recipient_phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('recipient_phone') is-invalid @enderror" 
                                       id="recipient_phone" name="recipient_phone" value="{{ old('recipient_phone', $parcel->recipient_phone) }}" required>
                                @error('recipient_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="recipient_country" class="form-label">Country <span class="text-danger">*</span></label>
                                <select class="form-select @error('recipient_country') is-invalid @enderror" 
                                        id="recipient_country" name="recipient_country" required>
                                    <option value="">Select Country</option>
                                    <option value="USA" {{ old('recipient_country', $parcel->recipient_country) === 'USA' ? 'selected' : '' }}>United States</option>
                                    <option value="Canada" {{ old('recipient_country', $parcel->recipient_country) === 'Canada' ? 'selected' : '' }}>Canada</option>
                                    <option value="Mexico" {{ old('recipient_country', $parcel->recipient_country) === 'Mexico' ? 'selected' : '' }}>Mexico</option>
                                    <option value="UK" {{ old('recipient_country', $parcel->recipient_country) === 'UK' ? 'selected' : '' }}>United Kingdom</option>
                                </select>
                                @error('recipient_country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="recipient_address" class="form-label">Street Address <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('recipient_address') is-invalid @enderror" 
                                      id="recipient_address" name="recipient_address" rows="2" required>{{ old('recipient_address', $parcel->recipient_address) }}</textarea>
                            @error('recipient_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="recipient_city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_city') is-invalid @enderror" 
                                       id="recipient_city" name="recipient_city" value="{{ old('recipient_city', $parcel->recipient_city) }}" required>
                                @error('recipient_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="recipient_state" class="form-label">State/Province <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_state') is-invalid @enderror" 
                                       id="recipient_state" name="recipient_state" value="{{ old('recipient_state', $parcel->recipient_state) }}" required>
                                @error('recipient_state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="recipient_postal_code" class="form-label">Postal Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_postal_code') is-invalid @enderror" 
                                       id="recipient_postal_code" name="recipient_postal_code" value="{{ old('recipient_postal_code', $parcel->recipient_postal_code) }}" required>
                                @error('recipient_postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-box me-2"></i>Package Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="description" class="form-label">Package Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" required 
                                      placeholder="Describe the contents of the package...">{{ old('description', $parcel->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="weight" class="form-label">Weight (kg) <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('weight') is-invalid @enderror" 
                                       id="weight" name="weight" value="{{ old('weight', $parcel->weight) }}" required>
                                @error('weight')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="dimensions" class="form-label">Dimensions (L×W×H)</label>
                                <input type="text" class="form-control @error('dimensions') is-invalid @enderror" 
                                       id="dimensions" name="dimensions" value="{{ old('dimensions', $parcel->dimensions) }}" 
                                       placeholder="e.g., 30×20×10 cm">
                                @error('dimensions')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="declared_value" class="form-label">Declared Value ($) <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('declared_value') is-invalid @enderror" 
                                       id="declared_value" name="declared_value" value="{{ old('declared_value', $parcel->declared_value) }}" required>
                                @error('declared_value')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="special_instructions" class="form-label">Special Instructions</label>
                            <textarea class="form-control @error('special_instructions') is-invalid @enderror" 
                                      id="special_instructions" name="special_instructions" rows="2" 
                                      placeholder="Any special handling instructions...">{{ old('special_instructions', $parcel->special_instructions) }}</textarea>
                            @error('special_instructions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Shipping Options -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-shipping-fast me-2"></i>Shipping Options
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="carrier_id" class="form-label">Carrier <span class="text-danger">*</span></label>
                            <select class="form-select @error('carrier_id') is-invalid @enderror" 
                                    id="carrier_id" name="carrier_id" required onchange="updateShippingCost()">
                                <option value="">Select Carrier</option>
                                @foreach($carriers as $carrier)
                                    <option value="{{ $carrier->id }}" 
                                            data-base-rate="{{ $carrier->base_rate }}" 
                                            data-per-kg-rate="{{ $carrier->per_kg_rate }}"
                                            {{ old('carrier_id', $parcel->carrier_id) == $carrier->id ? 'selected' : '' }}>
                                        {{ $carrier->name }} ({{ $carrier->code }})
                                    </option>
                                @endforeach
                            </select>
                            @error('carrier_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="service_type" class="form-label">Service Type <span class="text-danger">*</span></label>
                            <select class="form-select @error('service_type') is-invalid @enderror" 
                                    id="service_type" name="service_type" required onchange="updateShippingCost()">
                                <option value="">Select Service</option>
                                <option value="standard" {{ old('service_type', $parcel->service_type) === 'standard' ? 'selected' : '' }}>Standard</option>
                                <option value="express" {{ old('service_type', $parcel->service_type) === 'express' ? 'selected' : '' }}>Express (+20%)</option>
                                <option value="overnight" {{ old('service_type', $parcel->service_type) === 'overnight' ? 'selected' : '' }}>Overnight (+50%)</option>
                                <option value="same_day" {{ old('service_type', $parcel->service_type) === 'same_day' ? 'selected' : '' }}>Same Day (+100%)</option>
                            </select>
                            @error('service_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="estimated_delivery_date" class="form-label">Estimated Delivery Date</label>
                            <input type="date" class="form-control @error('estimated_delivery_date') is-invalid @enderror" 
                                   id="estimated_delivery_date" name="estimated_delivery_date" 
                                   value="{{ old('estimated_delivery_date', $parcel->estimated_delivery_date?->format('Y-m-d')) }}">
                            @error('estimated_delivery_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Cost Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>Cost Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="shipping_cost" class="form-label">Shipping Cost ($) <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" min="0" class="form-control @error('shipping_cost') is-invalid @enderror" 
                                   id="shipping_cost" name="shipping_cost" value="{{ old('shipping_cost', $parcel->shipping_cost) }}" required>
                            @error('shipping_cost')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="insurance_cost" class="form-label">Insurance Cost ($)</label>
                            <input type="number" step="0.01" min="0" class="form-control @error('insurance_cost') is-invalid @enderror" 
                                   id="insurance_cost" name="insurance_cost" value="{{ old('insurance_cost', $parcel->insurance_cost ?? 0) }}" onchange="updateTotalCost()">
                            @error('insurance_cost')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Total Cost ($)</label>
                            <div class="form-control bg-light" id="total_cost_display">@currency($parcel->total_cost)</div>
                        </div>
                    </div>
                </div>

                <!-- Customer Assignment -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Customer Assignment
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="user_id" class="form-label">Assign to Customer</label>
                            <select class="form-select @error('user_id') is-invalid @enderror" 
                                    id="user_id" name="user_id">
                                <option value="">No Customer (Guest)</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}" {{ old('user_id', $parcel->user_id) == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->name }} ({{ $customer->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Optional: Link this parcel to an existing customer</small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Update Parcel
                            </button>
                            <a href="{{ route('admin.parcels.show', $parcel) }}" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                            <a href="{{ route('admin.parcels.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('scripts')
<script>
    // Auto-calculate shipping cost based on weight, carrier, and service type
    function updateShippingCost() {
        const carrierSelect = document.getElementById('carrier_id');
        const serviceSelect = document.getElementById('service_type');
        const weightInput = document.getElementById('weight');
        const shippingCostInput = document.getElementById('shipping_cost');
        
        if (carrierSelect.value && weightInput.value && serviceSelect.value) {
            const selectedOption = carrierSelect.options[carrierSelect.selectedIndex];
            const baseRate = parseFloat(selectedOption.dataset.baseRate) || 0;
            const perKgRate = parseFloat(selectedOption.dataset.perKgRate) || 0;
            const weight = parseFloat(weightInput.value) || 0;
            
            let cost = baseRate + (perKgRate * weight);
            
            // Apply service type multiplier
            switch (serviceSelect.value) {
                case 'express':
                    cost *= 1.2;
                    break;
                case 'overnight':
                    cost *= 1.5;
                    break;
                case 'same_day':
                    cost *= 2.0;
                    break;
            }
            
            shippingCostInput.value = cost.toFixed(2);
            updateTotalCost();
        }
    }
    
    // Update total cost
    function updateTotalCost() {
        const shippingCost = parseFloat(document.getElementById('shipping_cost').value) || 0;
        const insuranceCost = parseFloat(document.getElementById('insurance_cost').value) || 0;
        const totalCost = shippingCost + insuranceCost;
        
        document.getElementById('total_cost_display').textContent = formatCurrency(totalCost);
    }
    
    // Event listeners
    document.getElementById('weight').addEventListener('input', updateShippingCost);
    document.getElementById('insurance_cost').addEventListener('input', updateTotalCost);
    document.getElementById('shipping_cost').addEventListener('input', updateTotalCost);
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        updateTotalCost();
    });
</script>
@endpush
