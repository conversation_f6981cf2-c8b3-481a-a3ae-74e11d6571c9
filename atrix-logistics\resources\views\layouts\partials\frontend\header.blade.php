<!-- Top Bar -->
<div class="bg-gray-800 text-white py-2 hidden md:block">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center text-sm">
            <div class="flex items-center space-x-6">
                @if(isset($siteSettings['contact_email']))
                <a href="mailto:{{ $siteSettings['contact_email'] }}" class="flex items-center hover:text-green-400 transition-colors">
                    <i class="fas fa-envelope mr-2"></i>
                    {{ $siteSettings['contact_email'] }}
                </a>
                @endif
                @if(isset($siteSettings['contact_phone']))
                <a href="tel:{{ $siteSettings['contact_phone'] }}" class="flex items-center hover:text-green-400 transition-colors">
                    <i class="fas fa-phone mr-2"></i>
                    {{ $siteSettings['contact_phone'] }}
                </a>
                @endif
            </div>
            <div class="flex items-center space-x-4">
                @if(isset($siteSettings['business_hours']))
                <span class="flex items-center">
                    <i class="fas fa-clock mr-2"></i>
                    {{ $siteSettings['business_hours'] }}
                </span>
                @endif

                <!-- Language & Currency Dropdowns -->
                <div class="flex items-center space-x-3">
                    @include('components.language-dropdown')
                    @include('components.currency-dropdown')
                </div>

                <div class="flex items-center space-x-2">
                    @if(isset($siteSettings['social_facebook']))
                    <a href="{{ $siteSettings['social_facebook'] }}" target="_blank" class="hover:text-green-400 transition-colors">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    @endif
                    @if(isset($siteSettings['social_twitter']))
                    <a href="{{ $siteSettings['social_twitter'] }}" target="_blank" class="hover:text-green-400 transition-colors">
                        <i class="fab fa-twitter"></i>
                    </a>
                    @endif
                    @if(isset($siteSettings['social_linkedin']))
                    <a href="{{ $siteSettings['social_linkedin'] }}" target="_blank" class="hover:text-green-400 transition-colors">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{{ route('home') }}" class="flex items-center">
                    @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
                        <img src="{{ Storage::url($siteSettings['site_logo']) }}" alt="{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}" class="h-12 w-auto">
                    @else
                        <div class="flex items-center">
                            <div class="bg-green-600 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-shipping-fast text-xl"></i>
                            </div>
                            <div>
                                <h1 class="text-xl font-bold text-gray-800">{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</h1>
                                @if(isset($siteSettings['site_tagline']))
                                <p class="text-xs text-gray-600">{{ $siteSettings['site_tagline'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden lg:flex items-center space-x-8">
                <a href="{{ route('home') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors {{ request()->routeIs('home') ? 'text-green-600' : '' }}">
                    Home
                </a>

                <!-- Products Dropdown (Clickable + Hover) -->
                <div class="relative group">
                    <a href="{{ route('products.index') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors flex items-center {{ request()->routeIs('products.*') || request()->routeIs('categories.*') ? 'text-green-600' : '' }}">
                        Products
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </a>
                    <div class="absolute top-full left-0 mt-2 w-72 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                        <div class="py-4">
                            <a href="{{ route('products.index') }}" class="block px-6 py-2 text-gray-700 hover:text-green-600 hover:bg-green-50 transition-colors font-semibold border-b border-gray-100 mb-2">
                                <i class="fas fa-th-large mr-3 text-green-600"></i>
                                All Products
                            </a>
                            <a href="{{ route('categories.show', 'shipping-containers') }}" class="block px-6 py-2 text-gray-700 hover:text-green-600 hover:bg-green-50 transition-colors">
                                <i class="fas fa-cube mr-3 text-blue-600"></i>
                                Shipping Containers for Sale
                            </a>
                            <a href="{{ route('categories.show', 'cardboard-boxes') }}" class="block px-6 py-2 text-gray-700 hover:text-green-600 hover:bg-green-50 transition-colors">
                                <i class="fas fa-box mr-3 text-orange-600"></i>
                                CardBoxes for Sale
                            </a>
                            <a href="{{ route('categories.show', 'steel-products') }}" class="block px-6 py-2 text-gray-700 hover:text-green-600 hover:bg-green-50 transition-colors">
                                <i class="fas fa-industry mr-3 text-gray-600"></i>
                                Steel for Sale
                            </a>
                        </div>
                    </div>
                </div>

                <a href="{{ route('services') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors {{ request()->routeIs('services') ? 'text-green-600' : '' }}">
                    Services
                </a>

                <a href="{{ route('tracking.index') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors {{ request()->routeIs('tracking.*') ? 'text-green-600' : '' }}">
                    Track Shipment
                </a>

                <a href="{{ route('about') }}#shipping-calculator" class="text-gray-700 hover:text-green-600 font-medium transition-colors" onclick="scrollToCalculator(event)">
                    Shipping Calculator
                </a>

                <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors {{ request()->routeIs('blog.*') ? 'text-green-600' : '' }}">
                    Blog
                </a>

                <a href="{{ route('about') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors {{ request()->routeIs('about') ? 'text-green-600' : '' }}">
                    About
                </a>

                <a href="{{ route('contact') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors {{ request()->routeIs('contact') ? 'text-green-600' : '' }}">
                    Contact
                </a>
            </nav>

            <!-- CTA Buttons -->
            <div class="hidden lg:flex items-center space-x-4">
                <button onclick="openQuoteModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 btn-primary">
                    Get Quote
                </button>
                @auth
                    <a href="{{ route('customer.dashboard') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors">
                        Dashboard
                    </a>
                @else
                    <a href="{{ route('customer.login') }}" class="text-gray-700 hover:text-green-600 font-medium transition-colors">
                        Login
                    </a>
                @endauth
            </div>

            <!-- Mobile Menu Button -->
            <button onclick="toggleMobileMenu()" class="lg:hidden text-gray-700 hover:text-green-600 transition-colors">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>
    </div>
</header>

<!-- Mobile Menu -->
<div id="mobile-menu" class="fixed top-0 right-0 h-full w-80 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 z-50 lg:hidden">
    <div class="p-6">
        <!-- Mobile Menu Header -->
        <div class="flex justify-between items-center mb-8">
            <div class="flex items-center">
                @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
                    <img src="{{ Storage::url($siteSettings['site_logo']) }}" alt="{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}" class="h-8 w-auto">
                @else
                    <div class="flex items-center">
                        <div class="bg-primary-600 text-white p-1 rounded mr-2">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <span class="font-bold text-secondary-800">{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</span>
                    </div>
                @endif
            </div>
            <button onclick="toggleMobileMenu()" class="text-secondary-700 hover:text-primary-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Mobile Navigation -->
        <nav class="space-y-4">
            <a href="{{ route('home') }}" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors {{ request()->routeIs('home') ? 'text-green-600' : '' }}">
                Home
            </a>

            <!-- Mobile Products Dropdown -->
            <div class="space-y-2">
                <button onclick="toggleMobileDropdown('products')" class="flex justify-between items-center w-full text-gray-700 hover:text-green-600 font-medium py-2 transition-colors">
                    Products
                    <i class="fas fa-chevron-down text-xs" id="products-icon"></i>
                </button>
                <div id="products-dropdown" class="hidden pl-4 space-y-2">
                    <a href="{{ route('products.index') }}" class="block text-gray-600 hover:text-green-600 py-1 transition-colors font-semibold">
                        <i class="fas fa-th-large mr-2 text-green-600"></i>
                        All Products
                    </a>
                    <a href="{{ route('categories.show', 'shipping-containers') }}" class="block text-gray-600 hover:text-green-600 py-1 transition-colors">
                        <i class="fas fa-cube mr-2 text-blue-600"></i>
                        Shipping Containers for Sale
                    </a>
                    <a href="{{ route('categories.show', 'cardboard-boxes') }}" class="block text-gray-600 hover:text-green-600 py-1 transition-colors">
                        <i class="fas fa-box mr-2 text-orange-600"></i>
                        CardBoxes for Sale
                    </a>
                    <a href="{{ route('categories.show', 'steel-products') }}" class="block text-gray-600 hover:text-green-600 py-1 transition-colors">
                        <i class="fas fa-industry mr-2 text-gray-600"></i>
                        Steel for Sale
                    </a>
                </div>
            </div>

            <a href="{{ route('services') }}" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors {{ request()->routeIs('services') ? 'text-green-600' : '' }}">
                Services
            </a>

            <a href="{{ route('tracking.index') }}" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors {{ request()->routeIs('tracking.*') ? 'text-green-600' : '' }}">
                Track Shipment
            </a>

            <a href="{{ route('about') }}#shipping-calculator" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors" onclick="scrollToCalculator(event)">
                Shipping Calculator
            </a>

            <a href="{{ route('blog.index') }}" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors {{ request()->routeIs('blog.*') ? 'text-green-600' : '' }}">
                Blog
            </a>

            <a href="{{ route('about') }}" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors {{ request()->routeIs('about') ? 'text-green-600' : '' }}">
                About
            </a>

            <a href="{{ route('contact') }}" class="block text-gray-700 hover:text-green-600 font-medium py-2 transition-colors {{ request()->routeIs('contact') ? 'text-green-600' : '' }}">
                Contact
            </a>
        </nav>

        <!-- Mobile CTA Buttons -->
        <div class="mt-8 space-y-4">
            <button onclick="openQuoteModal()" class="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300">
                Get Quote
            </button>
            @auth
                <a href="{{ route('customer.dashboard') }}" class="block text-center text-gray-700 hover:text-green-600 font-medium py-2 transition-colors">
                    Dashboard
                </a>
            @else
                <a href="{{ route('customer.login') }}" class="block text-center text-gray-700 hover:text-green-600 font-medium py-2 transition-colors">
                    Login
                </a>
            @endauth
        </div>
    </div>
</div>

<script>
function toggleMobileDropdown(id) {
    const dropdown = document.getElementById(id + '-dropdown');
    const icon = document.getElementById(id + '-icon');

    if (dropdown.classList.contains('hidden')) {
        dropdown.classList.remove('hidden');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        dropdown.classList.add('hidden');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}

function scrollToCalculator(event) {
    // If we're already on the about page, just scroll to the calculator
    if (window.location.pathname === '/about') {
        event.preventDefault();
        const calculatorSection = document.getElementById('shipping-calculator');
        if (calculatorSection) {
            calculatorSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
    // If we're on a different page, let the link navigate normally
    // The about page will handle scrolling on load if there's a hash
}
</script>
