<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(Request $request): View
    {
        $query = Product::with(['category'])
                       ->active()
                       ->orderBy('sort_order')
                       ->orderBy('created_at', 'desc');

        // Filter by category
        if ($request->filled('category')) {
            $category = Category::where('slug', $request->category)->first();
            if ($category) {
                $query->where('category_id', $category->id);
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sort options
        $sortBy = $request->get('sort', 'newest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            default: // newest
                $query->orderBy('created_at', 'desc');
                break;
        }

        $products = $query->paginate(12);
        
        // Get categories for filter
        $categories = Category::active()
                            ->whereNull('parent_id')
                            ->with('children')
                            ->orderBy('sort_order')
                            ->get();

        // Get price range for filter
        $priceRange = Product::active()
                           ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                           ->first();

        // Get site settings for the layout
        $siteSettings = \App\Models\SiteSetting::getPublicSettings();

        return view('frontend.products.index', compact(
            'products',
            'categories',
            'priceRange',
            'siteSettings'
        ));
    }

    /**
     * Display the specified product
     */
    public function show(Product $product): View
    {
        // Ensure product is active
        if (!$product->is_active) {
            abort(404);
        }

        // Load relationships
        $product->load(['category']);

        // Get related products
        $relatedProducts = Product::active()
                                ->where('id', '!=', $product->id)
                                ->where('category_id', $product->category_id)
                                ->limit(4)
                                ->get();

        // If not enough related products from same category, get from other categories
        if ($relatedProducts->count() < 4) {
            $additionalProducts = Product::active()
                                       ->where('id', '!=', $product->id)
                                       ->whereNotIn('id', $relatedProducts->pluck('id'))
                                       ->limit(4 - $relatedProducts->count())
                                       ->get();
            
            $relatedProducts = $relatedProducts->merge($additionalProducts);
        }

        // Get site settings for the layout
        $siteSettings = \App\Models\SiteSetting::getPublicSettings();

        return view('frontend.products.show', compact('product', 'relatedProducts', 'siteSettings'));
    }
}
