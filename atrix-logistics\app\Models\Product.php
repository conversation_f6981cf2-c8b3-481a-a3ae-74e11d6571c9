<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'sku',
        'category_id',
        'description',
        'additional_information',
        'technical_specifications',
        'short_description',
        'price',
        'sale_price',
        'cost_price',
        'weight',
        'dimensions',
        'stock_quantity',
        'min_stock_level',
        'manage_stock',
        'stock_status',
        'images',
        'featured_image',
        'gallery_images',
        'is_active',
        'is_featured',
        'is_digital',
        'is_virtual',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'tags',
        'attributes',
        'variations',
        'shipping_class',
        'tax_class',
        'reviews_allowed',
        'average_rating',
        'review_count',
        'total_sales',
        'date_on_sale_from',
        'date_on_sale_to',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'stock_quantity' => 'integer',
        'min_stock_level' => 'integer',
        'sort_order' => 'integer',
        'average_rating' => 'decimal:1',
        'review_count' => 'integer',
        'total_sales' => 'integer',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'is_digital' => 'boolean',
        'is_virtual' => 'boolean',
        'manage_stock' => 'boolean',
        'reviews_allowed' => 'boolean',
        'images' => 'array',
        'gallery_images' => 'array',
        'dimensions' => 'array',
        'tags' => 'array',
        'attributes' => 'array',
        'variations' => 'array',
        'date_on_sale_from' => 'datetime',
        'date_on_sale_to' => 'datetime',
    ];

    /**
     * Get the category that owns the product
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }





    /**
     * Get the current price (sale price if on sale, otherwise regular price)
     */
    public function getCurrentPriceAttribute(): float
    {
        if ($this->isOnSale()) {
            return $this->sale_price;
        }

        return $this->price;
    }

    /**
     * Get the current price as a method (for compatibility)
     */
    public function getCurrentPrice(): float
    {
        return $this->getCurrentPriceAttribute();
    }

    /**
     * Get discount percentage
     */
    public function getDiscountPercentage(): int
    {
        if (!$this->isOnSale()) {
            return 0;
        }

        return round((($this->price - $this->sale_price) / $this->price) * 100);
    }



    /**
     * Check if product is on sale
     */
    public function isOnSale(): bool
    {
        if (!$this->sale_price || $this->sale_price >= $this->price) {
            return false;
        }

        $now = now();

        if ($this->date_on_sale_from && $now < $this->date_on_sale_from) {
            return false;
        }

        if ($this->date_on_sale_to && $now > $this->date_on_sale_to) {
            return false;
        }

        return true;
    }

    /**
     * Get discount percentage
     */
    public function getDiscountPercentageAttribute(): ?float
    {
        if (!$this->isOnSale()) {
            return null;
        }

        return round((($this->price - $this->sale_price) / $this->price) * 100);
    }

    /**
     * Check if product is in stock
     */
    public function isInStock(): bool
    {
        if (!$this->manage_stock) {
            return $this->stock_status === 'in_stock';
        }

        return $this->stock_quantity > 0;
    }

    /**
     * Check if product is low in stock
     */
    public function isLowStock(): bool
    {
        if (!$this->manage_stock || !$this->min_stock_level) {
            return false;
        }

        return $this->stock_quantity <= $this->min_stock_level && $this->stock_quantity > 0;
    }



    /**
     * Get stock status text
     */
    public function getStockStatusTextAttribute(): string
    {
        if (!$this->manage_stock) {
            return match($this->stock_status) {
                'in_stock' => 'In Stock',
                'out_of_stock' => 'Out of Stock',
                'on_backorder' => 'On Backorder',
                default => 'Unknown'
            };
        }

        if ($this->stock_quantity <= 0) {
            return 'Out of Stock';
        }

        if ($this->isLowStock()) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    /**
     * Get featured image URL
     */
    public function getFeaturedImageUrlAttribute(): ?string
    {
        if ($this->featured_image) {
            return \Storage::url($this->featured_image);
        }

        if (!empty($this->images)) {
            return \Storage::url($this->images[0]);
        }

        return null;
    }

    /**
     * Get gallery image URLs
     */
    public function getGalleryImageUrlsAttribute(): array
    {
        if (empty($this->gallery_images)) {
            return [];
        }

        return array_map(fn($image) => \Storage::url($image), $this->gallery_images);
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured products
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true)->where('is_active', true);
    }

    /**
     * Scope for products in stock
     */
    public function scopeInStock($query)
    {
        return $query->where(function($q) {
            $q->where('manage_stock', false)
              ->where('stock_status', 'in_stock')
              ->orWhere(function($q2) {
                  $q2->where('manage_stock', true)
                     ->where('stock_quantity', '>', 0);
              });
        });
    }

    /**
     * Scope for products on sale
     */
    public function scopeOnSale($query)
    {
        return $query->whereNotNull('sale_price')
                    ->whereColumn('sale_price', '<', 'price')
                    ->where(function($q) {
                        $q->whereNull('date_on_sale_from')
                          ->orWhere('date_on_sale_from', '<=', now());
                    })
                    ->where(function($q) {
                        $q->whereNull('date_on_sale_to')
                          ->orWhere('date_on_sale_to', '>=', now());
                    });
    }

    /**
     * Generate unique SKU
     */
    public static function generateSku(string $name, ?int $excludeId = null): string
    {
        $sku = strtoupper(\Str::slug($name, ''));
        $sku = substr($sku, 0, 8);
        $originalSku = $sku;
        $counter = 1;

        while (static::where('sku', $sku)
                    ->when($excludeId, fn($query) => $query->where('id', '!=', $excludeId))
                    ->exists()) {
            $sku = $originalSku . $counter;
            $counter++;
        }

        return $sku;
    }

    /**
     * Generate slug from name
     */
    public static function generateSlug(string $name, ?int $excludeId = null): string
    {
        $slug = \Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)
                    ->when($excludeId, fn($query) => $query->where('id', '!=', $excludeId))
                    ->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }



    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = static::generateSlug($product->name);
            }

            if (empty($product->sku)) {
                $product->sku = static::generateSku($product->name);
            }

            if (is_null($product->sort_order)) {
                $product->sort_order = static::max('sort_order') + 1;
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name')) {
                if (empty($product->slug)) {
                    $product->slug = static::generateSlug($product->name, $product->id);
                }

                if (empty($product->sku)) {
                    $product->sku = static::generateSku($product->name, $product->id);
                }
            }
        });
    }
}
